'use client'

import { useEffect } from 'react'

/**
 * Layout Shift Debug Component
 *
 * Logs layout shifts to console when NEXT_PUBLIC_DEBUG_CLS=1
 * Only runs in browser and when debug flag is enabled
 *
 * Usage: Import once in app/layout.tsx
 */
export default function LayoutShiftDebug() {
  useEffect(() => {
    // Debug: Always log component mount and environment check
    console.log('[CLS Debug] Component mounted')
    console.log('[CLS Debug] Environment check:', {
      isClient: typeof window !== 'undefined',
      debugFlag: process.env.NEXT_PUBLIC_DEBUG_CLS,
      shouldRun: typeof window !== 'undefined' && process.env.NEXT_PUBLIC_DEBUG_CLS === '1',
    })

    // Only run in browser and when debug flag is enabled
    if (typeof window === 'undefined' || process.env.NEXT_PUBLIC_DEBUG_CLS !== '1') {
      console.log('[CLS Debug] Skipping - not in browser or debug flag not set')
      return
    }

    // Check if PerformanceObserver is supported
    if (!('PerformanceObserver' in window)) {
      console.warn('[CLS Debug] PerformanceObserver not supported')
      return
    }

    let totalCLS = 0
    const shifts: Array<{
      value: number
      timestamp: number
      element: string
      hadRecentInput: boolean
    }> = []

    const observer = new PerformanceObserver(list => {
      list.getEntries().forEach(entry => {
        const layoutShiftEntry = entry as PerformanceEntry & {
          value: number
          hadRecentInput: boolean
          sources?: Array<{
            node?: Element
            currentRect?: DOMRectReadOnly
            previousRect?: DOMRectReadOnly
          }>
        }

        // Only count shifts not caused by user input
        if (!layoutShiftEntry.hadRecentInput) {
          totalCLS += layoutShiftEntry.value

          const elementInfo = layoutShiftEntry.sources?.[0]?.node
            ? getElementInfo(layoutShiftEntry.sources[0].node)
            : 'Unknown element'

          const shiftData = {
            value: layoutShiftEntry.value,
            timestamp: entry.startTime,
            element: elementInfo,
            hadRecentInput: layoutShiftEntry.hadRecentInput,
          }

          shifts.push(shiftData)

          // Log individual shift
          console.log(`%c[CLS] ${layoutShiftEntry.value.toFixed(4)}`, 'color: #ff6b6b; font-weight: bold;', {
            element: elementInfo,
            value: layoutShiftEntry.value,
            timestamp: entry.startTime,
            totalCLS: totalCLS.toFixed(4),
            domNode: layoutShiftEntry.sources?.[0]?.node,
          })

          // Log summary every 5 shifts or if CLS > 0.1
          if (shifts.length % 5 === 0 || totalCLS > 0.1) {
            console.table(shifts.slice(-10)) // Show last 10 shifts
            console.log(
              `%c[CLS Summary] Total: ${totalCLS.toFixed(4)} (${shifts.length} shifts)`,
              'color: #4ecdc4; font-weight: bold; font-size: 14px;'
            )
          }
        }
      })
    })

    try {
      observer.observe({ type: 'layout-shift', buffered: true })
      console.log('%c[CLS Debug] Layout shift monitoring started', 'color: #45b7d1; font-weight: bold;')
    } catch (error) {
      console.error('[CLS Debug] Failed to start observer:', error)
    }

    // Cleanup function
    return () => {
      observer.disconnect()
      console.log(
        `%c[CLS Debug] Final CLS: ${totalCLS.toFixed(4)} (${shifts.length} shifts)`,
        'color: #96ceb4; font-weight: bold; font-size: 16px;'
      )
    }
  }, [])

  // This component renders nothing
  return null
}

/**
 * Get human-readable element information
 */
function getElementInfo(element: Element): string {
  const tag = element.tagName.toLowerCase()
  const id = element.id ? `#${element.id}` : ''
  const classes = element.className ? `.${element.className.toString().split(' ').filter(Boolean).join('.')}` : ''

  // Get some context about the element
  const rect = element.getBoundingClientRect()
  const size = `${Math.round(rect.width)}x${Math.round(rect.height)}`

  return `${tag}${id}${classes} (${size})`
}
