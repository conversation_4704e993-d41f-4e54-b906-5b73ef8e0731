Metrics Expand view First Contentful Paint 0.7 s Largest Contentful Paint 1.1 s
Total Blocking Time 210 ms Cumulative Layout Shift 0.078 Speed Index 1.4 s
Captured at Jun 12, 2025, 1:35 AM GMT+3 Emulated Desktop with Lighthouse
12.6.1Unthrottled CPU/Memory Power: 401 CPU throttling: 1x slowdown (Simulated)
Screen emulation: 1350x940, DPR 1 Axe version: 4.10.3 Single page session
Initial page load Custom throttling Using HeadlessChromium 136.0.7103.113 with
lr

Show audits relevant to:

All

FCP

LCP

TBT

CLS Insights Improve image delivery Est savings of 537 KiB Reducing the download
time of images can improve the perceived load time of the page and LCP. Learn
more about optimizing image sizeLCPFCP URL Resource Size Est Savings ponyclub.gr
1st party 526.1 KiB 287.9 KiB
/\_next/image?url=%2Fimages%2Fhero-image.webp&w=1920&q=85(www.ponyclub.gr) 178.9
KiB 84.8 KiB Increasing the image compression factor could improve this image's
download size. 84.8 KiB /images/hero-image.webp(www.ponyclub.gr) 178.9 KiB 84.8
KiB Increasing the image compression factor could improve this image's download
size. 84.8 KiB
/\_next/image?url=%2Fimages%2Fround2.jpg&w=384&q=75(www.ponyclub.gr) 40.0 KiB
31.8 KiB Increasing the image compression factor could improve this image's
download size. 26.8 KiB This image file is larger than it needs to be (384x210)
for its displayed dimensions (183x275). Use responsive images to reduce the
image download size. 15.1 KiB
/\_next/image?url=%2Fimages%2Fround1.jpg&w=384&q=75(www.ponyclub.gr) 27.3 KiB
24.2 KiB Increasing the image compression factor could improve this image's
download size. 14.2 KiB This image file is larger than it needs to be (384x209)
for its displayed dimensions (183x106). Use responsive images to reduce the
image download size. 20.7 KiB
/\_next/image?url=%2Fimages%2Fround3.jpg&w=384&q=75(www.ponyclub.gr) 23.7 KiB
19.6 KiB Increasing the image compression factor could improve this image's
download size. 10.7 KiB This image file is larger than it needs to be (384x209)
for its displayed dimensions (183x137). Use responsive images to reduce the
image download size. 16.3 KiB
/\_next/image?url=%2Fimages%2Fponyclub_logo.png&w=256&q=75(www.ponyclub.gr) 16.4
KiB 13.9 KiB Increasing the image compression factor could improve this image's
download size. 13.9 KiB
/\_next/image?url=%2Fimages%2FRafting….jpg&w=384&q=75(www.ponyclub.gr) 24.4 KiB
12.4 KiB Increasing the image compression factor could improve this image's
download size. 12.4 KiB
/\_next/image?url=%2Fimages%2Fround2.jpg&w=192&q=75(www.ponyclub.gr) 13.1 KiB
7.1 KiB Increasing the image compression factor could improve this image's
download size. 7.1 KiB
/\_next/image?url=%2Fimages%2Fround1.jpg&w=192&q=75(www.ponyclub.gr) 6.9 KiB 4.9
KiB Increasing the image compression factor could improve this image's download
size. 4.9 KiB
/\_next/image?url=%2Fimages%2FKayaker….jpg&w=384&q=75(www.ponyclub.gr) 16.5 KiB
4.5 KiB Increasing the image compression factor could improve this image's
download size. 4.5 KiB Other Google APIs/SDKs utility 252.7 KiB 249.3 KiB
/a-/ALV-UjXo6…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 31.1 KiB 30.9 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 28.8 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 27.7 KiB
/a-/ALV-UjWBx…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 29.0 KiB 28.7 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 26.6 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 25.7 KiB
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.5 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 26.4 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 25.6 KiB
/a-/ALV-UjUCi…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.5 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 26.4 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 25.6 KiB
/a-/ALV-UjUFq…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 25.6 KiB 25.4 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 23.3 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 22.8 KiB
/a-/ALV-UjVXn…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 24.0 KiB 23.7
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 21.6 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 21.3 KiB
/a-/ALV-UjVLJ…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 21.4 KiB 21.2 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 19.1 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 19.0 KiB
/a-/ALV-UjUA5…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 18.9 KiB 18.6
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 16.5 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 16.8 KiB
/a-/ALV-UjXXV…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 15.9 KiB 15.6
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 13.5 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 14.1 KiB
/a/ACg8ocK2B…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 7.5 KiB 7.3 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 5.2 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.7 KiB
/a/ACg8ocIX4…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 7.5 KiB 7.3 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 5.2 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.7 KiB
/a/ACg8ocJlt…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 7.2 KiB 6.9 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 4.8 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.4 KiB
/a/ACg8ocIDs…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 7.0 KiB 6.8 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 4.7 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.3 KiB Legacy JavaScript Est savings
of 76 KiB Forced reflow LCP request discovery Use efficient cache lifetimes Est
savings of 447 KiB Layout shift culprits LCP by phase 3rd parties These insights
are also available in the Chrome DevTools Performance Panel - record a trace to
view more detailed information. Diagnostics Avoid serving legacy JavaScript to
modern browsers Est savings of 24 KiB Polyfills and transforms enable legacy
browsers to use new JavaScript features. However, many aren't necessary for
modern browsers. Consider modifying your JavaScript build process to not
transpile Baseline features, unless you know you must support legacy browsers.
Learn why most sites can deploy ES6+ code without transpilingLCPFCP URL Est
Savings ponyclub.gr 1st party 24.3 KiB
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 13.0 KiB
…chunks/797-1d16e539206e578b.js:18:70632(www.ponyclub.gr) Array.prototype.at
…chunks/797-1d16e539206e578b.js:18:70020(www.ponyclub.gr) Array.prototype.flat
…chunks/797-1d16e539206e578b.js:18:70133(www.ponyclub.gr)
Array.prototype.flatMap
…chunks/797-1d16e539206e578b.js:18:70509(www.ponyclub.gr) Object.fromEntries
…chunks/797-1d16e539206e578b.js:18:70767(www.ponyclub.gr) Object.hasOwn
…chunks/797-1d16e539206e578b.js:18:69762(www.ponyclub.gr)
String.prototype.trimEnd
…chunks/797-1d16e539206e578b.js:18:69677(www.ponyclub.gr)
String.prototype.trimStart …chunks/7f358c6e-b3c55055b0d02466.js(www.ponyclub.gr)
11.0 KiB …chunks/7f358c6e-b3c55055b0d02466.js:1:52368(www.ponyclub.gr)
Array.from …chunks/945-7b4c98ba143510df.js(www.ponyclub.gr) 0.3 KiB
…chunks/945-7b4c98ba143510df.js:1:46621(www.ponyclub.gr)
@babel/plugin-transform-classes Reduce unused JavaScript Est savings of 234 KiB
Reduce unused JavaScript and defer loading scripts until they are required to
decrease bytes consumed by network activity. Learn how to reduce unused
JavaScript.LCPFCP URL Transfer Size Est Savings ponyclub.gr 1st party 155.7 KiB
83.6 KiB …chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 116.6 KiB 49.6 KiB
…chunks/507-8a62939ad235c32b.js(www.ponyclub.gr) 39.1 KiB 34.0 KiB Google Tag
Manager tag-manager 148.2 KiB 79.6 KiB
/gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com) 148.2 KiB 79.6 KiB bokun.io
138.8 KiB 71.0 KiB /BokunWidgets.7edd992….js(static.bokun.io) 138.8 KiB 71.0 KiB
Defer offscreen images Est savings of 89 KiB Consider lazy-loading offscreen and
hidden images after all critical resources have finished loading to lower time
to interactive. Learn how to defer offscreen images.LCPFCP URL Resource Size Est
Savings Other Google APIs/SDKs utility 88.9 KiB 88.9 KiB Google reviewer profile
picture 1
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjWBxT2rgteH4h0v7ZwHM9jDZghBMlhU1…" alt="Google reviewer profile picture 1">
/a-/ALV-UjWBx…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 29.0 KiB 29.0 KiB
Google reviewer profile picture 5
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjXA4lsc4ituHxMAxvrdHK-XwdxjRlCit…" alt="Google reviewer profile picture 5">
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.8 KiB
Google reviewer profile picture 3
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjUA5N5C4d-LLl2YDed1dwtswJD5_5B6s…" alt="Google reviewer profile picture 3">
/a-/ALV-UjUA5…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 18.9 KiB 18.9
KiB Google reviewer profile picture 6
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocIpKgyfr0LoXX3QgMvA8J2dJW41pY3nQA…" alt="Google reviewer profile picture 6">
/a/ACg8ocIpK…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 4.4 KiB 4.4 KiB
Google reviewer profile picture 2
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocKlzpWOlPBsWu79xmPTiNx6pAGR8IE5hA…" alt="Google reviewer profile picture 2">
/a/ACg8ocKlz…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 4.3 KiB 4.3 KiB
Google reviewer profile picture 4
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocJcBpyGTl_gDhTURTmpD3DB6dKPEUE3Vc…" alt="Google reviewer profile picture 4">
/a/ACg8ocJcB…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 3.5 KiB 3.5 KiB
Avoid enormous network payloads Total size was 5,107 KiB Large network payloads
cost users real money and are highly correlated with long load times. Learn how
to reduce payload sizes. URL Transfer Size ponyclub.gr 1st party 2,409.7 KiB
/images/hero-video.mp4(www.ponyclub.gr) 1,932.9 KiB
/\_next/image?url=%2Fimages%2Fhero-image.webp&w=1920&q=85(www.ponyclub.gr) 179.7
KiB /images/hero-image.webp(www.ponyclub.gr) 179.6 KiB
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 117.5 KiB bokun.io 297.9 KiB
/BokunWidgets.7edd992….js(static.bokun.io) 139.4 KiB
/OnlineSal….5cbfb12….js(static.bokun.io) 86.4 KiB
/46929.fb94355….js(static.bokun.io) 72.1 KiB Google Maps utility 161.6 KiB
…api/js?key=AIzaSyB9j…(maps.googleapis.com) 87.0 KiB
…4a/util.js(maps.googleapis.com) 74.5 KiB Google Tag Manager tag-manager 149.2
KiB /gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com) 149.2 KiB Avoid long
main-thread tasks 5 long tasks found User Timing marks and measures 2 user
timings More information about the performance of your application. These
numbers don't directly affect the Performance score. Passed audits (17) Show 92
Accessibility These checks highlight opportunities to improve the accessibility
of your web app. Automatic detection can only detect a subset of issues and does
not guarantee the accessibility of your web app, so manual testing is also
encouraged. Contrast Background and foreground colors do not have a sufficient
contrast ratio. These are opportunities to improve the legibility of your
content. Names and labels

<frame> or <iframe> elements do not have a title
Screen reader users rely on frame titles to describe the contents of frames. Learn more about frame titles.
Failing Elements
div.lg:w-3/5 > div.h-[400px] > div > iframe
<iframe loading="lazy" src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBwaJVGFhnhN-WKtiLn6KS…" referrerpolicy="no-referrer-when-downgrade" frameborder="0" style="border: 0;" allowfullscreen="" width="100%" height="400">
